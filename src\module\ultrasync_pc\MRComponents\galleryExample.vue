<template>
    <div class="gallery-example">
        <button @click="openGallery" class="open-btn">打开画廊</button>
        
        <!-- 使用带右侧插槽的画廊组件 -->
        <base-gallery 
            ref="gallery"
            :loading="loading"
            :default-show-right-sidebar="true"
            :default-right-sidebar-width="350"
        >
            <!-- 右侧插槽内容 -->
            <template #rightSidebar="{ currentFile, currentIndex }">
                <div class="custom-sidebar">
                    <h3>文件详情</h3>
                    <div class="file-details">
                        <p><strong>文件名:</strong> {{ currentFile.label || '未知' }}</p>
                        <p><strong>类型:</strong> {{ currentFile.fileType || '未知' }}</p>
                        <p><strong>索引:</strong> {{ currentIndex + 1 }} / {{ fileList.length }}</p>
                        <p><strong>URL:</strong> {{ currentFile.url || '无' }}</p>
                    </div>
                    
                    <div class="actions">
                        <h4>操作</h4>
                        <button @click="downloadFile(currentFile)" class="action-button">
                            <i class="el-icon-download"></i> 下载
                        </button>
                        <button @click="shareFile(currentFile)" class="action-button">
                            <i class="el-icon-share"></i> 分享
                        </button>
                        <button @click="deleteFile(currentFile, currentIndex)" class="action-button danger">
                            <i class="el-icon-delete"></i> 删除
                        </button>
                    </div>
                    
                    <div class="metadata" v-if="currentFile.metadata">
                        <h4>元数据</h4>
                        <div class="metadata-item" v-for="(value, key) in currentFile.metadata" :key="key">
                            <span class="key">{{ key }}:</span>
                            <span class="value">{{ value }}</span>
                        </div>
                    </div>
                </div>
            </template>
        </base-gallery>
    </div>
</template>

<script>
import BaseGallery from './baseGallery.vue'

export default {
    name: 'GalleryExample',
    components: {
        BaseGallery
    },
    data() {
        return {
            loading: false,
            fileList: [
                {
                    url: 'https://example.com/image1.jpg',
                    fileType: 'image',
                    label: '示例图片1',
                    metadata: {
                        '拍摄时间': '2024-01-01',
                        '分辨率': '1920x1080',
                        '大小': '2.5MB'
                    }
                },
                {
                    url: 'https://example.com/video1.mp4',
                    fileType: 'video',
                    label: '示例视频1',
                    thumbnail: 'https://example.com/video1-thumb.jpg',
                    metadata: {
                        '时长': '00:05:30',
                        '分辨率': '1920x1080',
                        '大小': '50MB'
                    }
                },
                {
                    url: 'https://example.com/document1.pdf',
                    fileType: 'pdf',
                    label: '示例文档1',
                    metadata: {
                        '页数': '10',
                        '大小': '1.2MB'
                    }
                }
            ]
        }
    },
    methods: {
        openGallery() {
            this.$refs.gallery.openGallery(this.fileList, 0)
        },
        
        downloadFile(file) {
            console.log('下载文件:', file)
            this.$message.success('开始下载: ' + file.label)
        },
        
        shareFile(file) {
            console.log('分享文件:', file)
            this.$message.success('分享链接已复制: ' + file.label)
        },
        
        deleteFile(file, index) {
            this.$confirm('确定要删除这个文件吗?', '确认删除', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                console.log('删除文件:', file, '索引:', index)
                this.$message.success('文件已删除: ' + file.label)
            }).catch(() => {
                this.$message.info('已取消删除')
            })
        }
    }
}
</script>

<style lang="scss" scoped>
.gallery-example {
    padding: 20px;
    
    .open-btn {
        background: #409EFF;
        color: white;
        border: none;
        padding: 12px 24px;
        border-radius: 4px;
        cursor: pointer;
        font-size: 16px;
        
        &:hover {
            background: #66b1ff;
        }
    }
}

.custom-sidebar {
    h3, h4 {
        color: #fff;
        margin: 0 0 15px 0;
        font-size: 16px;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        padding-bottom: 8px;
    }
    
    .file-details {
        margin-bottom: 20px;
        
        p {
            margin: 8px 0;
            color: #ccc;
            font-size: 14px;
            
            strong {
                color: #fff;
            }
        }
    }
    
    .actions {
        margin-bottom: 20px;
        
        .action-button {
            display: block;
            width: 100%;
            margin: 8px 0;
            padding: 8px 12px;
            background: #333;
            color: #fff;
            border: 1px solid #555;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.2s;
            
            &:hover {
                background: #444;
                border-color: #666;
            }
            
            &.danger {
                background: #f56c6c;
                border-color: #f56c6c;
                
                &:hover {
                    background: #f78989;
                    border-color: #f78989;
                }
            }
            
            i {
                margin-right: 5px;
            }
        }
    }
    
    .metadata {
        .metadata-item {
            display: flex;
            justify-content: space-between;
            margin: 8px 0;
            font-size: 14px;
            
            .key {
                color: #fff;
                font-weight: 500;
            }
            
            .value {
                color: #ccc;
            }
        }
    }
}
</style>
